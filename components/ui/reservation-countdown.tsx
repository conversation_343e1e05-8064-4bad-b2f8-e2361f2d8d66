'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Clock, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { formatRemainingTime, isReservationExpiringSoon, releaseReservation } from '@/lib/reservation-utils';

interface ReservationCountdownProps {
  reservationId?: number;
  expiresAt?: string;
  userIdentifier: string;
  busRoute: string;
  busName: string;
  onExpired?: () => void;
  onReleased?: () => void;
  className?: string;
}

export function ReservationCountdown({
  reservationId,
  expiresAt,
  userIdentifier,
  busRoute,
  busName,
  onExpired,
  onReleased,
  className = ''
}: ReservationCountdownProps) {
  const [remainingMinutes, setRemainingMinutes] = useState<number>(0);
  const [isExpired, setIsExpired] = useState(false);
  const [isReleasing, setIsReleasing] = useState(false);

  useEffect(() => {
    if (!expiresAt) return;

    const updateCountdown = () => {
      const now = new Date().getTime();
      const expiry = new Date(expiresAt).getTime();
      const remaining = (expiry - now) / (1000 * 60); // Convert to minutes

      if (remaining <= 0) {
        setRemainingMinutes(0);
        setIsExpired(true);
        onExpired?.();
      } else {
        setRemainingMinutes(remaining);
        setIsExpired(false);
      }
    };

    // Update immediately
    updateCountdown();

    // Update every second
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, [expiresAt, onExpired]);

  const handleRelease = async () => {
    if (!reservationId) return;

    setIsReleasing(true);
    try {
      const result = await releaseReservation(reservationId, userIdentifier, 'manual_release');
      if (result.success) {
        onReleased?.();
      }
    } catch (error) {
      console.error('Failed to release reservation:', error);
    } finally {
      setIsReleasing(false);
    }
  };

  if (!reservationId || !expiresAt) {
    return null;
  }

  const isExpiringSoon = isReservationExpiringSoon(remainingMinutes);

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        className={className}
      >
        <Card className={`border-2 ${
          isExpired 
            ? 'border-red-200 bg-red-50' 
            : isExpiringSoon 
              ? 'border-orange-200 bg-orange-50' 
              : 'border-green-200 bg-green-50'
        }`}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-full ${
                  isExpired 
                    ? 'bg-red-100' 
                    : isExpiringSoon 
                      ? 'bg-orange-100' 
                      : 'bg-green-100'
                }`}>
                  {isExpired ? (
                    <XCircle className="w-5 h-5 text-red-600" />
                  ) : isExpiringSoon ? (
                    <AlertTriangle className="w-5 h-5 text-orange-600" />
                  ) : (
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  )}
                </div>
                
                <div>
                  <div className="flex items-center space-x-2">
                    <h3 className="font-semibold text-gray-900">
                      {isExpired ? 'Reservation Expired' : 'Seat Reserved'}
                    </h3>
                    <Badge 
                      variant="outline" 
                      className={`text-xs ${
                        isExpired 
                          ? 'border-red-200 text-red-600 bg-red-50' 
                          : isExpiringSoon 
                            ? 'border-orange-200 text-orange-600 bg-orange-50' 
                            : 'border-green-200 text-green-600 bg-green-50'
                      }`}
                    >
                      {busRoute} - {busName}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center space-x-2 mt-1">
                    <Clock className="w-4 h-4 text-gray-500" />
                    <span className={`text-sm font-medium ${
                      isExpired 
                        ? 'text-red-600' 
                        : isExpiringSoon 
                          ? 'text-orange-600' 
                          : 'text-green-600'
                    }`}>
                      {isExpired 
                        ? 'Please select another bus' 
                        : `${formatRemainingTime(remainingMinutes)} remaining`
                      }
                    </span>
                  </div>
                </div>
              </div>

              {!isExpired && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRelease}
                  disabled={isReleasing}
                  className="text-gray-600 hover:text-red-600 hover:border-red-300"
                >
                  {isReleasing ? 'Releasing...' : 'Release'}
                </Button>
              )}
            </div>

            {isExpiringSoon && !isExpired && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="mt-3 p-2 bg-orange-100 border border-orange-200 rounded-md"
              >
                <p className="text-xs text-orange-700 flex items-center">
                  <AlertTriangle className="w-3 h-3 mr-1" />
                  Hurry! Your reservation expires soon. Complete your booking quickly.
                </p>
              </motion.div>
            )}

            {isExpired && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="mt-3 p-2 bg-red-100 border border-red-200 rounded-md"
              >
                <p className="text-xs text-red-700 flex items-center">
                  <XCircle className="w-3 h-3 mr-1" />
                  Your reservation has expired. The seat is now available for other users.
                </p>
              </motion.div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
}
