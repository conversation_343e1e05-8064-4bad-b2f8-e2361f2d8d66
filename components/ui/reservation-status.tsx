'use client';

import { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Clock, AlertTriangle, CheckCircle, RefreshCw } from 'lucide-react';
import { motion } from 'framer-motion';
import { checkReservationStatus, formatRemainingTime, isReservationExpiringSoon } from '@/lib/reservation-utils';
import { ReservationStatusResponse } from '@/lib/types';

interface ReservationStatusProps {
  userIdentifier: string;
  busRouteCode: string;
  departureTime: string;
  onStatusChange?: (status: ReservationStatusResponse) => void;
  autoRefresh?: boolean;
  refreshInterval?: number; // in seconds
  className?: string;
}

export function ReservationStatus({
  userIdentifier,
  busRouteCode,
  departureTime,
  onStatusChange,
  autoRefresh = true,
  refreshInterval = 30,
  className = ''
}: ReservationStatusProps) {
  const [status, setStatus] = useState<ReservationStatusResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchStatus = async () => {
    setIsLoading(true);
    try {
      const result = await checkReservationStatus(userIdentifier, busRouteCode, departureTime);
      setStatus(result);
      setLastUpdated(new Date());
      onStatusChange?.(result);
    } catch (error) {
      console.error('Error fetching reservation status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Initial fetch
    fetchStatus();

    // Set up auto-refresh if enabled
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(fetchStatus, refreshInterval * 1000);
      return () => clearInterval(interval);
    }
  }, [userIdentifier, busRouteCode, departureTime, autoRefresh, refreshInterval]);

  const handleManualRefresh = () => {
    fetchStatus();
  };

  if (!status) {
    return (
      <div className={`flex items-center justify-center p-4 ${className}`}>
        <div className="flex items-center space-x-2 text-gray-500">
          <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          <span className="text-sm">Checking reservation status...</span>
        </div>
      </div>
    );
  }

  if (!status.has_reservation) {
    return (
      <div className={`flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg ${className}`}>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
          <span className="text-sm text-gray-600">No active reservation</span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleManualRefresh}
          disabled={isLoading}
          className="text-gray-500 hover:text-gray-700"
        >
          <RefreshCw className={`w-3 h-3 ${isLoading ? 'animate-spin' : ''}`} />
        </Button>
      </div>
    );
  }

  const remainingMinutes = status.remaining_minutes || 0;
  const isExpired = remainingMinutes <= 0;
  const isExpiringSoon = isReservationExpiringSoon(remainingMinutes);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.2 }}
      className={`p-4 border-2 rounded-lg ${
        isExpired 
          ? 'border-red-200 bg-red-50' 
          : isExpiringSoon 
            ? 'border-orange-200 bg-orange-50' 
            : 'border-green-200 bg-green-50'
      } ${className}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-full ${
            isExpired 
              ? 'bg-red-100' 
              : isExpiringSoon 
                ? 'bg-orange-100' 
                : 'bg-green-100'
          }`}>
            {isExpired ? (
              <AlertTriangle className="w-4 h-4 text-red-600" />
            ) : isExpiringSoon ? (
              <AlertTriangle className="w-4 h-4 text-orange-600" />
            ) : (
              <CheckCircle className="w-4 h-4 text-green-600" />
            )}
          </div>
          
          <div>
            <div className="flex items-center space-x-2">
              <span className={`font-medium ${
                isExpired 
                  ? 'text-red-700' 
                  : isExpiringSoon 
                    ? 'text-orange-700' 
                    : 'text-green-700'
              }`}>
                {isExpired ? 'Reservation Expired' : 'Seat Reserved'}
              </span>
              <Badge 
                variant="outline" 
                className={`text-xs ${
                  isExpired 
                    ? 'border-red-200 text-red-600 bg-red-50' 
                    : isExpiringSoon 
                      ? 'border-orange-200 text-orange-600 bg-orange-50' 
                      : 'border-green-200 text-green-600 bg-green-50'
                }`}
              >
                ID: {status.reservation_id}
              </Badge>
            </div>
            
            <div className="flex items-center space-x-2 mt-1">
              <Clock className="w-3 h-3 text-gray-500" />
              <span className={`text-xs ${
                isExpired 
                  ? 'text-red-600' 
                  : isExpiringSoon 
                    ? 'text-orange-600' 
                    : 'text-green-600'
              }`}>
                {isExpired 
                  ? 'Expired - please select another bus' 
                  : `${formatRemainingTime(remainingMinutes)} remaining`
                }
              </span>
            </div>
          </div>
        </div>

        <Button
          variant="ghost"
          size="sm"
          onClick={handleManualRefresh}
          disabled={isLoading}
          className="text-gray-500 hover:text-gray-700"
        >
          <RefreshCw className={`w-3 h-3 ${isLoading ? 'animate-spin' : ''}`} />
        </Button>
      </div>

      {lastUpdated && (
        <div className="mt-2 text-xs text-gray-500">
          Last updated: {lastUpdated.toLocaleTimeString()}
        </div>
      )}

      {isExpiringSoon && !isExpired && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-3 p-2 bg-orange-100 border border-orange-200 rounded-md"
        >
          <p className="text-xs text-orange-700 flex items-center">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Warning: Your reservation expires in less than 2 minutes!
          </p>
        </motion.div>
      )}
    </motion.div>
  );
}
