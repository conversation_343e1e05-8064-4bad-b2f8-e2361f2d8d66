'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Clock, 
  Users, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  RefreshCw,
  Activity,
  TrendingUp
} from 'lucide-react';
import { motion } from 'framer-motion';
import { getReservationStatistics, performPeriodicCleanup } from '@/lib/reservation-utils';

interface ReservationStats {
  active_reservations: number;
  expired_reservations: number;
  completed_reservations: number;
  cancelled_reservations: number;
  total_reservations: number;
  oldest_active_reservation: string | null;
  newest_active_reservation: string | null;
  timestamp: string;
}

export function ReservationMonitor() {
  const [stats, setStats] = useState<ReservationStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isCleaningUp, setIsCleaningUp] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  const fetchStats = async () => {
    setIsLoading(true);
    try {
      const result = await getReservationStatistics();
      if (result.success && result.statistics) {
        setStats(result.statistics);
        setLastUpdated(new Date());
      }
    } catch (error) {
      console.error('Error fetching reservation statistics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCleanup = async () => {
    setIsCleaningUp(true);
    try {
      const result = await performPeriodicCleanup();
      if (result.success) {
        // Refresh stats after cleanup
        await fetchStats();
      }
    } catch (error) {
      console.error('Error performing cleanup:', error);
    } finally {
      setIsCleaningUp(false);
    }
  };

  useEffect(() => {
    // Initial fetch
    fetchStats();

    // Set up auto-refresh if enabled
    if (autoRefresh) {
      const interval = setInterval(fetchStats, 30000); // Every 30 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  if (!stats) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <div className="flex items-center space-x-2 text-gray-500">
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span>Loading reservation statistics...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalActive = stats.active_reservations;
  const totalProcessed = stats.completed_reservations + stats.cancelled_reservations + stats.expired_reservations;
  const successRate = totalProcessed > 0 ? (stats.completed_reservations / totalProcessed * 100) : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Reservation Monitor</h2>
          <p className="text-gray-600">Real-time seat reservation system statistics</p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={autoRefresh ? 'bg-green-50 border-green-200' : ''}
          >
            <Activity className="w-4 h-4 mr-1" />
            Auto-refresh {autoRefresh ? 'ON' : 'OFF'}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={fetchStats}
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleCleanup}
            disabled={isCleaningUp}
            className="text-orange-600 hover:text-orange-700"
          >
            <AlertTriangle className={`w-4 h-4 mr-1 ${isCleaningUp ? 'animate-pulse' : ''}`} />
            {isCleaningUp ? 'Cleaning...' : 'Cleanup'}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Active Reservations */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Reservations</CardTitle>
              <Clock className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{totalActive}</div>
              <p className="text-xs text-gray-600">Currently holding seats</p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Completed Reservations */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.completed_reservations}</div>
              <p className="text-xs text-gray-600">Successfully booked</p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Expired Reservations */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Expired</CardTitle>
              <XCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.expired_reservations}</div>
              <p className="text-xs text-gray-600">Timed out</p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Success Rate */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">{successRate.toFixed(1)}%</div>
              <p className="text-xs text-gray-600">Completion rate</p>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Detailed Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* System Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="w-5 h-5" />
              <span>System Status</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Total Reservations</span>
              <Badge variant="outline">{stats.total_reservations}</Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Cancelled</span>
              <Badge variant="outline" className="text-orange-600 border-orange-200">
                {stats.cancelled_reservations}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Last Updated</span>
              <span className="text-xs text-gray-500">
                {lastUpdated?.toLocaleTimeString() || 'Never'}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Auto Cleanup</span>
              <Badge variant="outline" className="text-green-600 border-green-200">
                Active
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Active Reservation Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="w-5 h-5" />
              <span>Active Reservations</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {totalActive === 0 ? (
              <p className="text-sm text-gray-500 text-center py-4">
                No active reservations
              </p>
            ) : (
              <>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Oldest Active</span>
                  <span className="text-xs text-gray-500">
                    {stats.oldest_active_reservation 
                      ? new Date(stats.oldest_active_reservation).toLocaleString()
                      : 'N/A'
                    }
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Newest Active</span>
                  <span className="text-xs text-gray-500">
                    {stats.newest_active_reservation 
                      ? new Date(stats.newest_active_reservation).toLocaleString()
                      : 'N/A'
                    }
                  </span>
                </div>
                
                <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <p className="text-xs text-blue-700">
                    💡 Active reservations are automatically cleaned up when they expire.
                    Use the cleanup button to manually remove expired reservations.
                  </p>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
