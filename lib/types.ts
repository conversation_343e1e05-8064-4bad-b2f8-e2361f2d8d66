export interface Bus {
  id: number;
  name: string;
  route: string;
}

export interface RouteStop {
  id: number;
  name: string;
  fare: number;
}

export interface Booking {
  booking_id: string;
  studentName: string;
  admissionNumber: string;
  busRoute: string;
  destination: string;
  paymentStatus: boolean;
  timestamp: string;
  goDate?: string | null;
  returnDate?: string | null;
  fare?: number | null;
  busName?: string | null;
  boarded?: boolean;
}

export interface CreateBookingRequest {
  booking_id: string;
  studentName: string;
  admissionNumber: string;
  busRoute: string;
  destination: string;
  paymentStatus?: boolean;
  timestamp: string;
  razorpay_payment_id?: string;
  razorpay_order_id?: string;
  razorpay_signature?: string;
}

export interface SeatReservation {
  id: number;
  session_id: string;
  user_identifier: string;
  bus_route_code: string;
  departure_time: string;
  seat_number?: number | null;
  reserved_at: string;
  expires_at: string;
  status: 'active' | 'expired' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
}

export interface CreateReservationRequest {
  session_id: string;
  user_identifier: string;
  bus_route_code: string;
  departure_time: string;
  reservation_duration_minutes?: number;
}

export interface ReservationResponse {
  success: boolean;
  reservation_id?: number;
  expires_at?: string;
  remaining_time_minutes?: number;
  error?: string;
  code?: string;
  available_seats?: number;
}

export interface ReservationStatusResponse {
  success: boolean;
  has_reservation: boolean;
  reservation_id?: number;
  expires_at?: string;
  remaining_minutes?: number;
  error?: string;
}

export interface EffectiveSeatAvailabilityResponse {
  success: boolean;
  total_seats?: number;
  active_reservations?: number;
  effective_available?: number;
  is_last_seat?: boolean;
  error?: string;
}

export interface AdminSettings {
  bookingEnabled: boolean;
  goDate: string | null;
  returnDate: string | null;
  busAvailability: { [busRoute: string]: number };
}

export interface NewBookingStats {
  totalBuses: number;
  totalBookings: number;
  currentBookings: number;
  paidBookings: number;
  unpaidBookings: number;
  currentRevenue: number;
  availableSeats: number;
  totalCapacity: number;
  occupancyRate: string;
}