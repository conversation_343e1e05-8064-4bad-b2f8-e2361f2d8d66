import { 
  CreateReservationRequest, 
  ReservationResponse, 
  ReservationStatusResponse, 
  EffectiveSeatAvailabilityResponse 
} from './types';

/**
 * Generate a unique session ID for reservation tracking
 */
export function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substring(2)}`;
}

/**
 * Create a seat reservation
 */
export async function createSeatReservation(
  sessionId: string,
  userIdentifier: string,
  busRouteCode: string,
  departureTime: string,
  reservationDurationMinutes: number = 10
): Promise<ReservationResponse> {
  try {
    const response = await fetch('/api/reservations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        session_id: sessionId,
        user_identifier: userIdentifier,
        bus_route_code: busRouteCode,
        departure_time: departureTime,
        reservation_duration_minutes: reservationDurationMinutes,
      } as CreateReservationRequest),
    });

    const data: ReservationResponse = await response.json();
    return data;
  } catch (error) {
    console.error('Error creating seat reservation:', error);
    return {
      success: false,
      error: 'Failed to create reservation',
      code: 'NETWORK_ERROR'
    };
  }
}

/**
 * Check reservation status
 */
export async function checkReservationStatus(
  userIdentifier: string,
  busRouteCode: string,
  departureTime: string
): Promise<ReservationStatusResponse> {
  try {
    const params = new URLSearchParams({
      user_identifier: userIdentifier,
      bus_route_code: busRouteCode,
      departure_time: departureTime,
    });

    const response = await fetch(`/api/reservations?${params}`);
    const data: ReservationStatusResponse = await response.json();
    return data;
  } catch (error) {
    console.error('Error checking reservation status:', error);
    return {
      success: false,
      has_reservation: false,
      error: 'Failed to check reservation status'
    };
  }
}

/**
 * Release/cancel a reservation
 */
export async function releaseReservation(
  reservationId: number,
  userIdentifier: string,
  reason: string = 'manual_release'
): Promise<{ success: boolean; error?: string; message?: string }> {
  try {
    const params = new URLSearchParams({
      reservation_id: reservationId.toString(),
      user_identifier: userIdentifier,
      reason: reason,
    });

    const response = await fetch(`/api/reservations?${params}`, {
      method: 'DELETE',
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error releasing reservation:', error);
    return {
      success: false,
      error: 'Failed to release reservation'
    };
  }
}

/**
 * Complete a reservation when booking is confirmed
 */
export async function completeReservation(
  userIdentifier: string,
  busRouteCode: string,
  departureTime: string,
  bookingId: string
): Promise<{ success: boolean; error?: string; message?: string }> {
  try {
    const response = await fetch('/api/reservations/complete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_identifier: userIdentifier,
        bus_route_code: busRouteCode,
        departure_time: departureTime,
        booking_id: bookingId,
      }),
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error completing reservation:', error);
    return {
      success: false,
      error: 'Failed to complete reservation'
    };
  }
}

/**
 * Get effective seat availability considering active reservations
 */
export async function getEffectiveSeatAvailability(
  busRouteCode: string,
  departureTime?: string
): Promise<EffectiveSeatAvailabilityResponse> {
  try {
    const params = new URLSearchParams({
      bus_route_code: busRouteCode,
    });

    if (departureTime) {
      params.append('departure_time', departureTime);
    }

    const response = await fetch(`/api/reservations/availability?${params}`);
    const data: EffectiveSeatAvailabilityResponse = await response.json();
    return data;
  } catch (error) {
    console.error('Error getting effective seat availability:', error);
    return {
      success: false,
      error: 'Failed to get seat availability'
    };
  }
}

/**
 * Trigger cleanup of expired reservations
 */
export async function cleanupExpiredReservations(): Promise<{ 
  success: boolean; 
  expired_count?: number; 
  error?: string; 
  message?: string 
}> {
  try {
    const response = await fetch('/api/reservations/cleanup', {
      method: 'POST',
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error cleaning up expired reservations:', error);
    return {
      success: false,
      error: 'Failed to cleanup expired reservations'
    };
  }
}

/**
 * Format remaining time for display
 */
export function formatRemainingTime(remainingMinutes: number): string {
  if (remainingMinutes <= 0) {
    return 'Expired';
  }

  const minutes = Math.floor(remainingMinutes);
  const seconds = Math.floor((remainingMinutes - minutes) * 60);

  if (minutes > 0) {
    return `${minutes}m ${seconds}s`;
  } else {
    return `${seconds}s`;
  }
}

/**
 * Check if reservation is about to expire (less than 2 minutes remaining)
 */
export function isReservationExpiringSoon(remainingMinutes: number): boolean {
  return remainingMinutes > 0 && remainingMinutes < 2;
}

/**
 * Get default departure time (current admin settings go_date or current time)
 */
export async function getDefaultDepartureTime(): Promise<string> {
  try {
    const response = await fetch('/api/admin/settings');
    const data = await response.json();

    if (data.adminData?.go_date) {
      return new Date(data.adminData.go_date).toISOString();
    }

    // Fallback to current time
    return new Date().toISOString();
  } catch (error) {
    console.error('Error getting default departure time:', error);
    return new Date().toISOString();
  }
}

/**
 * Get reservation statistics for monitoring
 */
export async function getReservationStatistics(): Promise<{
  success: boolean;
  statistics?: any;
  error?: string;
}> {
  try {
    const response = await fetch('/api/reservations/statistics');
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error getting reservation statistics:', error);
    return {
      success: false,
      error: 'Failed to get reservation statistics'
    };
  }
}

/**
 * Periodic cleanup function that can be called from components
 */
export async function performPeriodicCleanup(): Promise<{
  success: boolean;
  expired_count?: number;
  error?: string;
}> {
  try {
    // First, get statistics to see if cleanup is needed
    const statsResult = await getReservationStatistics();

    if (!statsResult.success) {
      return {
        success: false,
        error: 'Failed to check cleanup status'
      };
    }

    // Perform cleanup
    const cleanupResult = await cleanupExpiredReservations();

    return cleanupResult;
  } catch (error) {
    console.error('Error performing periodic cleanup:', error);
    return {
      success: false,
      error: 'Failed to perform periodic cleanup'
    };
  }
}

/**
 * Auto-cleanup hook for components that need to ensure clean state
 */
export function useReservationCleanup(intervalMinutes: number = 5) {
  if (typeof window === 'undefined') return; // Server-side guard

  const cleanup = async () => {
    try {
      await performPeriodicCleanup();
    } catch (error) {
      console.error('Auto-cleanup error:', error);
    }
  };

  // Cleanup on mount
  cleanup();

  // Set up periodic cleanup
  const interval = setInterval(cleanup, intervalMinutes * 60 * 1000);

  // Return cleanup function
  return () => clearInterval(interval);
}
