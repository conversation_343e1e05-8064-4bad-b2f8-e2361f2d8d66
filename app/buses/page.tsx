'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PageTransition } from '@/components/ui/page-transition';
import { Bus as BusIcon, Users, Activity, User, ChevronLeft, Clock, AlertTriangle } from 'lucide-react';
import { motion } from 'framer-motion';
import { toast } from 'sonner';
import { supabase } from '@/lib/supabase';
import { useBooking } from '@/contexts/BookingContext';
import { getEffectiveSeatAvailability, createSeatReservation, generateSessionId, getDefaultDepartureTime, useReservationCleanup } from '@/lib/reservation-utils';
import { EffectiveSeatAvailabilityResponse } from '@/lib/types';

interface BusRow {
  id: number;
  name: string;
  route_code: string;
  available_seats: number;
  is_active: boolean;
  effective_available?: number;
  is_last_seat?: boolean;
  active_reservations?: number;
}

export default function BusesPage() {
  const [buses, setBuses] = useState<BusRow[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [sessionId] = useState(() => generateSessionId());
  const [departureTime, setDepartureTime] = useState<string>('');
  const router = useRouter();
  const { bookingData, updateBookingData } = useBooking();

  // Set up periodic cleanup of expired reservations
  useEffect(() => {
    const cleanup = useReservationCleanup(5); // Cleanup every 5 minutes
    return cleanup;
  }, []);

  useEffect(() => {
    // Check if student details are available
    if (!bookingData.studentName || !bookingData.admissionNumber) {
      router.push('/');
      return;
    }

    // Initialize departure time and fetch buses
    const initializeData = async () => {
      const defaultDeparture = await getDefaultDepartureTime();
      setDepartureTime(defaultDeparture);
      await fetchBuses(defaultDeparture);
    };

    initializeData();
  }, [bookingData.studentName, bookingData.admissionNumber, router]);

  const fetchBuses = async (departure?: string) => {
    try {
      const { data, error } = await supabase
        .from('buses')
        .select('id, name, route_code, available_seats, is_active')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;

      // Enhance bus data with effective availability
      const enhancedBuses = await Promise.all(
        (data || []).map(async (bus) => {
          const availabilityResponse = await getEffectiveSeatAvailability(
            bus.route_code,
            departure || departureTime
          );

          if (availabilityResponse.success) {
            return {
              ...bus,
              effective_available: availabilityResponse.effective_available || 0,
              is_last_seat: availabilityResponse.is_last_seat || false,
              active_reservations: availabilityResponse.active_reservations || 0
            };
          }

          return {
            ...bus,
            effective_available: bus.available_seats,
            is_last_seat: bus.available_seats === 1,
            active_reservations: 0
          };
        })
      );

      setBuses(enhancedBuses);
    } catch (error) {
      toast.error('Failed to load buses');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBusSelect = async (bus: BusRow) => {
    const effectiveSeats = bus.effective_available ?? bus.available_seats ?? 0;

    if (effectiveSeats === 0) {
      toast.error('This bus is fully booked');
      return;
    }

    // CRITICAL: Always attempt reservation for last seat (effective_available === 1)
    // The database function will validate if it's truly the last seat scenario
    if (effectiveSeats === 1) {
      toast.loading('Reserving last seat...', { id: 'reservation' });

      try {
        console.log('🔒 Attempting last seat reservation:', {
          sessionId,
          userIdentifier: bookingData.admissionNumber,
          busRoute: bus.route_code,
          departureTime,
          effectiveSeats,
          isLastSeat: bus.is_last_seat
        });

        const reservationResult = await createSeatReservation(
          sessionId,
          bookingData.admissionNumber,
          bus.route_code,
          departureTime,
          3 // 3 minutes reservation
        );

        console.log('🔒 Reservation result:', reservationResult);

        if (reservationResult.success) {
          toast.success('Last seat reserved! You have 3 minutes to complete booking.', {
            id: 'reservation'
          });

          // Store reservation info in booking context
          updateBookingData({
            busRoute: bus.route_code,
            busName: bus.name,
            reservationId: reservationResult.reservation_id,
            reservationExpiresAt: reservationResult.expires_at
          });

          router.push(`/buses/${bus.route_code}/destinations`);
        } else {
          console.error('❌ Reservation failed:', reservationResult);
          toast.error(reservationResult.error || 'Failed to reserve seat', {
            id: 'reservation'
          });

          // Refresh bus data to show updated availability
          await fetchBuses();
        }
      } catch (error) {
        console.error('❌ Reservation error:', error);
        toast.error('Failed to reserve seat. Please try again.', {
          id: 'reservation'
        });

        // Refresh bus data in case of error
        await fetchBuses();
      }
    } else {
      // Normal flow for non-last-seat scenarios
      console.log('✅ Normal booking flow (not last seat):', {
        busRoute: bus.route_code,
        effectiveSeats
      });

      updateBookingData({
        busRoute: bus.route_code,
        busName: bus.name
      });
      router.push(`/buses/${bus.route_code}/destinations`);
    }
  };

  if (isLoading) {
    return (
      <PageTransition direction="right">
        <div className="min-h-screen flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="w-full max-w-md"
          >
            <Card className="shadow-2xl border-0 bg-white/90 backdrop-blur-sm">
              <CardContent className="p-8 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Loading buses...</p>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </PageTransition>
    );
  }

  return (
    <PageTransition direction="right">
      <div className="min-h-screen p-4">
        {/* Back Button - now outside the main container and spaced from top */}
        <div className="max-w-6xl mx-auto">
          <motion.button
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
            onClick={() => router.push('/details')}
            className="flex items-center text-blue-600 hover:text-blue-800 transition-colors mb-6 mt-2 sm:mb-8 sm:mt-4"
          >
            <ChevronLeft className="w-5 h-5 sm:w-6 sm:h-6" />
            <span className="text-sm sm:text-base font-medium ml-1">Back</span>
          </motion.button>
        </div>
        <div className="max-w-6xl mx-auto">
          {/* Student Info Display */}
          <div className="mb-6 bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200 shadow-sm">
            <div className="flex items-center gap-2 mb-2">
              <div className="bg-white p-1.5 rounded-full shadow-sm">
                <User className="w-5 h-5 text-blue-600" />
              </div>
              <h2 className="text-lg font-semibold text-blue-900">Student Information</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm pl-2">
              <div>
                <span className="text-gray-600">Name: </span>
                <span className="font-semibold text-gray-800">{bookingData.studentName}</span>
              </div>
              <div>
                <span className="text-gray-600">Admission Number: </span>
                <span className="font-semibold text-gray-800">{bookingData.admissionNumber}</span>
              </div>
            </div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-8"
          >
            <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-700 to-green-600 text-transparent bg-clip-text mb-2">
              Select Your Bus
            </h1>
            <p className="text-gray-600">Choose from available buses below</p>
          </motion.div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {buses.map((bus, index) => (
              <motion.div
                key={bus.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.05 }}
                className="h-full"
              >
                <Card
                  className={`h-full overflow-hidden rounded-xl border-0 ${
                    (bus.effective_available ?? 0) === 0
                      ? 'bg-gray-50 opacity-70 cursor-not-allowed shadow-md'
                      : bus.is_last_seat
                        ? 'cursor-pointer bg-white hover:bg-gradient-to-br hover:from-white hover:to-orange-50 shadow-md hover:shadow-2xl transition-all duration-500 hover:translate-y-[-4px] ring-2 ring-orange-200'
                        : 'cursor-pointer bg-white hover:bg-gradient-to-br hover:from-white hover:to-blue-50 shadow-md hover:shadow-2xl transition-all duration-500 hover:translate-y-[-4px]'
                  }`}
                  onClick={() => handleBusSelect(bus)}
                >
                  {/* Top gradient bar */}
                  <div className={`h-2.5 w-full ${
                    (bus.effective_available ?? 0) === 0
                      ? 'bg-gray-300'
                      : bus.is_last_seat
                        ? 'bg-gradient-to-r from-orange-500 via-red-500 to-orange-600'
                        : 'bg-gradient-to-r from-blue-600 via-blue-500 to-green-500'
                  }`}
                  ></div>
                  
                  <CardContent className="p-0">
                    {/* Header with bus icon and availability */}
                    <div className="relative p-5 pb-3 flex justify-between items-start">
                      <div className={`rounded-full p-3.5 flex-shrink-0 shadow-sm ${
                        (bus.available_seats ?? 0) === 0 
                          ? 'bg-gray-100 text-gray-400' 
                          : 'bg-gradient-to-br from-blue-50 to-blue-100 text-blue-700'
                      }`}>
                        <BusIcon className="w-7 h-7" />
                      </div>
                      
                      <div className="flex gap-2 flex-wrap justify-end">
                        {/* Available seats badge */}
                        <Badge
                          variant="outline"
                          className={`text-xs py-1.5 px-2.5 font-medium rounded-full shadow-sm ${
                            (bus.effective_available ?? 0) === 0
                              ? 'bg-red-50 text-red-600 border-red-200'
                              : bus.is_last_seat
                                ? 'bg-orange-50 text-orange-600 border-orange-200 animate-pulse'
                                : (bus.effective_available ?? 0) < 5
                                  ? 'bg-amber-50 text-amber-600 border-amber-200 group hover:bg-amber-100 hover:border-amber-300 transition-all duration-300'
                                  : 'bg-green-50 text-green-600 border-green-200 group hover:bg-green-100 hover:border-green-300 transition-all duration-300'
                          }`}
                        >
                          {bus.is_last_seat ? (
                            <AlertTriangle className="w-3 h-3 mr-1.5" />
                          ) : (
                            <Users className="w-3 h-3 mr-1.5" />
                          )}
                          <span className="whitespace-nowrap flex items-center">
                            <span className="hidden sm:inline-block sm:opacity-0 sm:max-w-0 group-hover:max-w-xs group-hover:opacity-100 transition-all duration-300 overflow-hidden">
                              {bus.is_last_seat ? 'Last Seat!' : 'Seats:'}&nbsp;
                            </span>
                            <span className="sm:hidden">{bus.is_last_seat ? 'Last!' : 'Seats:'}&nbsp;</span>
                            <span className="font-bold">{bus.effective_available ?? 0}</span>
                            {(bus.active_reservations ?? 0) > 0 && (
                              <span className="text-xs ml-1 opacity-75">
                                ({bus.active_reservations} reserved)
                              </span>
                            )}
                          </span>
                        </Badge>
                        
                        {/* Status badge */}
                        <Badge 
                          variant="outline" 
                          className={`text-xs py-1.5 px-2.5 font-medium rounded-full shadow-sm ${
                            bus.is_active 
                              ? 'border-green-200 text-green-600 bg-green-50' 
                              : 'border-gray-200 text-gray-500 bg-gray-50'
                          }`}
                        >
                          <Activity className="w-3 h-3 mr-1.5" />
                          {bus.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                    </div>
                    
                    {/* Divider with subtle gradient */}
                    <div className="h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent"></div>
                    
                    {/* Bus details */}
                    <div className="p-5 pt-3.5">
                      <h3 className={`text-lg font-bold mb-2 ${
                        (bus.available_seats ?? 0) === 0 
                          ? 'text-gray-500' 
                          : 'text-gray-800'
                      }`}>
                        {bus.name}
                      </h3>
                      <div className="flex items-center">
                        <div className={`flex items-center text-sm ${
                          (bus.available_seats ?? 0) === 0 
                            ? 'text-gray-400' 
                            : 'text-gray-600'
                        }`}>
                          <span className="inline-flex items-center justify-center h-5 w-5 rounded-full mr-2 bg-blue-100 border border-blue-200">
                            <span className="inline-block w-2 h-2 rounded-full bg-blue-500"></span>
                          </span>
                          Route: <span className="font-medium ml-1">{bus.route_code}</span>
                        </div>
                      </div>
                    </div>
                    
                    {/* Action hint with gradient background */}
                    <div className={`text-center py-2.5 text-xs font-semibold tracking-wide ${
                      (bus.effective_available ?? 0) === 0
                        ? 'bg-gray-200 text-gray-500'
                        : bus.is_last_seat
                          ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-inner animate-pulse'
                          : 'bg-gradient-to-r from-blue-600 to-green-500 text-white shadow-inner'
                    }`}>
                      {(bus.effective_available ?? 0) === 0
                        ? 'FULLY BOOKED'
                        : bus.is_last_seat
                          ? 'RESERVE LAST SEAT'
                          : 'SELECT THIS BUS'}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </PageTransition>
  );
}