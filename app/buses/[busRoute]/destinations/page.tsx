'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PageTransition } from '@/components/ui/page-transition';
import { MapPin, ArrowRight, User, ChevronLeft } from 'lucide-react';
import Link from 'next/link';
import { useBooking } from '@/contexts/BookingContext';

interface Params {
  params: { busRoute: string };
}

export default function DestinationsPage({ params }: Params) {
  const { bookingData } = useBooking();
  const [bus, setBus] = useState<any>(null);
  const [stops, setStops] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  const routeCode = decodeURIComponent(params.busRoute);

  useEffect(() => {
    // Check if student details are available
    if (!bookingData.studentName || !bookingData.admissionNumber) {
      router.push('/');
      return;
    }

    const fetchData = async () => {
      try {
        const { data: busData } = await supabase
          .from('buses')
          .select('name, route_code, available_seats')
          .eq('route_code', routeCode)
          .eq('is_active', true)
          .single();

        const { data: stopsData } = await supabase
          .from('route_stops')
          .select('id, stop_name, fare, stop_order')
          .eq('route_code', routeCode)
          .eq('is_active', true)
          .order('stop_order');

        setBus(busData);
        setStops(stopsData || []);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [routeCode, bookingData.studentName, bookingData.admissionNumber, router]);

  if (loading) {
    return (
      <PageTransition>
        <div className="min-h-screen p-4 flex items-center justify-center">
          <div className="w-full max-w-md">
            <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
              <CardContent className="p-8 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Loading destinations...</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </PageTransition>
    );
  }

  return (
    <PageTransition>
      <div className="min-h-screen p-4">
        {/* Back Button - above student info card */}
        <div className="max-w-4xl mx-auto">
          <button
            onClick={() => router.push('/buses')}
            className="flex items-center text-blue-600 hover:text-blue-800 transition-colors mb-6 mt-2 sm:mb-8 sm:mt-4"
          >
            <ChevronLeft className="w-5 h-5 sm:w-6 sm:h-6" />
            <span className="text-sm sm:text-base font-medium ml-1">Back</span>
          </button>
        </div>
        <div className="max-w-4xl mx-auto">
          {/* Student Info Display */}
          <div className="mb-6 bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200 shadow-sm">
            <div className="flex items-center gap-2 mb-2">
              <div className="bg-white p-1.5 rounded-full shadow-sm">
                <User className="w-5 h-5 text-blue-600" />
              </div>
              <h2 className="text-lg font-semibold text-blue-900">Student Information</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm pl-2">
              <div>
                <span className="text-gray-600">Name: </span>
                <span className="font-semibold text-gray-800">{bookingData.studentName}</span>
              </div>
              <div>
                <span className="text-gray-600">Admission Number: </span>
                <span className="font-semibold text-gray-800">{bookingData.admissionNumber}</span>
              </div>
            </div>
          </div>

          <div className="mb-8">
            <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-700 to-green-600 text-transparent bg-clip-text mb-2">{bus?.name}</h1>
            <div className="flex flex-wrap gap-3 items-center">
              <p className="text-gray-600 flex items-center">
                <span className="inline-flex items-center justify-center h-5 w-5 rounded-full mr-2 bg-blue-100 border border-blue-200">
                  <span className="inline-block w-2 h-2 rounded-full bg-blue-500"></span>
                </span>
                Route: <span className="font-medium ml-1">{bus?.route_code}</span>
              </p>
              <Badge 
                variant="outline"
                className="py-1.5 px-2.5 rounded-full shadow-sm border-green-200 text-green-600 bg-green-50"
              >
                Available Seats: <span className="font-bold ml-1">{bus?.available_seats ?? 0}</span>
              </Badge>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {stops?.map((stop) => (
              <Link 
                key={stop.id} 
                href={`/buses/${routeCode}/destinations/${encodeURIComponent(stop.stop_name)}`}
                className="group block focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 rounded-xl"
                aria-label={`Select destination: ${stop.stop_name}, Fare: ₹${stop.fare}, approximately ${Math.max(5, stop.stop_order * 10)} minutes travel time`}
              >
                <Card className="h-full border border-gray-200 group-hover:border-blue-300 shadow-sm group-hover:shadow-lg transition-all duration-300 overflow-hidden rounded-xl bg-white group-hover:bg-gradient-to-br group-hover:from-white group-hover:to-blue-50 group-active:scale-[0.98]">
                  <CardContent className="p-0">
                    {/* Top colored bar */}
                    <div className="h-2 bg-gradient-to-r from-blue-600 to-green-500"></div>
                    
                    <div className="p-5 flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start gap-3 mb-3">
                          <div className="bg-gradient-to-br from-red-50 to-red-100 p-2 rounded-full flex-shrink-0 shadow-sm border border-red-200">
                            <MapPin className="w-5 h-5 text-red-600" />
                          </div>
                          <div>
                            <h3 className="font-bold text-gray-800 text-lg leading-tight mb-1">{stop.stop_name}</h3>
                            <p className="text-sm text-gray-500">
                              <span className="inline-flex items-center">
                                <span className="inline-block w-1.5 h-1.5 rounded-full bg-blue-500 mr-1.5"></span>
                                Stop #{stop.stop_order}
                              </span>
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex flex-wrap gap-3 text-sm mt-3">
                          {/* Fare badge */}
                          <div className="bg-blue-50 px-3 py-1.5 rounded-full flex items-center border border-blue-100 shadow-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-blue-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span className="font-medium text-blue-700">₹{stop.fare}</span>
                          </div>
                          
                        </div>
                      </div>
                      
                      <div className="ml-4 bg-blue-100 group-hover:bg-blue-200 rounded-full p-3 transition-all duration-300 shadow-sm border border-blue-200">
                        <ArrowRight className="w-5 h-5 text-blue-700" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </PageTransition>
  );
}