'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';

export default function TestReservationPage() {
  const [busRoute, setBusRoute] = useState('ROUTE_001');
  const [departureTime, setDepartureTime] = useState(
    new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().slice(0, 16)
  );
  const [userIdentifier, setUserIdentifier] = useState('TEST_USER');
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<any[]>([]);
  const [debugInfo, setDebugInfo] = useState<any>(null);

  const generateSessionId = () => {
    return `test_session_${Date.now()}_${Math.random().toString(36).substring(2)}`;
  };

  const createReservation = async (userId: string) => {
    const sessionId = generateSessionId();
    const startTime = Date.now();
    
    try {
      const response = await fetch('/api/reservations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: sessionId,
          user_identifier: `${userIdentifier}_${userId}`,
          bus_route_code: busRoute,
          departure_time: new Date(departureTime).toISOString(),
          reservation_duration_minutes: 3
        })
      });

      const data = await response.json();
      const duration = Date.now() - startTime;

      return {
        userId,
        sessionId,
        success: data.success,
        status: response.status,
        data,
        duration,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      return {
        userId,
        sessionId,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime,
        timestamp: new Date().toISOString()
      };
    }
  };

  const testConcurrentReservations = async () => {
    setIsLoading(true);
    setResults([]);

    try {
      console.log('🔥 Starting concurrent reservation test...');
      
      // Launch 3 concurrent requests
      const promises = ['A', 'B', 'C'].map(userId => createReservation(userId));
      const results = await Promise.all(promises);
      
      setResults(results);
      
      // Get debug info after test
      await getDebugInfo();
      
    } catch (error) {
      console.error('Test error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getDebugInfo = async () => {
    try {
      const params = new URLSearchParams({
        bus_route_code: busRoute,
        departure_time: new Date(departureTime).toISOString()
      });

      const response = await fetch(`/api/debug/reservations?${params}`);
      const data = await response.json();
      setDebugInfo(data.debug_info);
    } catch (error) {
      console.error('Error getting debug info:', error);
    }
  };

  const forceCleanup = async () => {
    try {
      const response = await fetch('/api/debug/reservations?action=cleanup', {
        method: 'POST'
      });
      const data = await response.json();
      console.log('Cleanup result:', data);
      await getDebugInfo();
    } catch (error) {
      console.error('Cleanup error:', error);
    }
  };

  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>🧪 Seat Reservation Race Condition Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="busRoute">Bus Route Code</Label>
              <Input
                id="busRoute"
                value={busRoute}
                onChange={(e) => setBusRoute(e.target.value)}
                placeholder="ROUTE_001"
              />
            </div>
            <div>
              <Label htmlFor="departureTime">Departure Time</Label>
              <Input
                id="departureTime"
                type="datetime-local"
                value={departureTime}
                onChange={(e) => setDepartureTime(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="userIdentifier">User Identifier Base</Label>
              <Input
                id="userIdentifier"
                value={userIdentifier}
                onChange={(e) => setUserIdentifier(e.target.value)}
                placeholder="TEST_USER"
              />
            </div>
          </div>

          <div className="flex gap-2">
            <Button 
              onClick={testConcurrentReservations} 
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? 'Testing...' : '🔥 Test Concurrent Reservations'}
            </Button>
            <Button 
              onClick={getDebugInfo} 
              variant="outline"
            >
              🔍 Get Debug Info
            </Button>
            <Button 
              onClick={forceCleanup} 
              variant="outline"
              className="text-orange-600 hover:text-orange-700"
            >
              🧹 Force Cleanup
            </Button>
          </div>
        </CardContent>
      </Card>

      {results.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📊 Test Results
              {successful.length === 1 ? (
                <Badge className="bg-green-600">✅ PASS</Badge>
              ) : successful.length === 0 ? (
                <Badge variant="outline">⚠️ NO SUCCESS</Badge>
              ) : (
                <Badge variant="destructive">❌ RACE CONDITION</Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{successful.length}</div>
                <div className="text-sm text-gray-600">Successful</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{failed.length}</div>
                <div className="text-sm text-gray-600">Failed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{results.length}</div>
                <div className="text-sm text-gray-600">Total</div>
              </div>
            </div>

            <div className="space-y-2">
              {results.map((result, index) => (
                <div 
                  key={index}
                  className={`p-3 rounded border ${
                    result.success 
                      ? 'bg-green-50 border-green-200' 
                      : 'bg-red-50 border-red-200'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="font-medium">
                        User {result.userId} 
                        {result.success ? ' ✅' : ' ❌'}
                      </div>
                      <div className="text-sm text-gray-600">
                        {result.success 
                          ? `Reserved seat (ID: ${result.data?.reservation_id})`
                          : `${result.data?.error || result.error} (${result.data?.code || 'UNKNOWN'})`
                        }
                      </div>
                    </div>
                    <div className="text-xs text-gray-500">
                      {result.duration}ms
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {debugInfo && (
        <Card>
          <CardHeader>
            <CardTitle>🔍 Debug Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <h4 className="font-medium mb-2">Bus Information</h4>
                <div className="text-sm space-y-1">
                  <div>Route: {debugInfo.bus_data?.route_code || 'N/A'}</div>
                  <div>Available Seats: {debugInfo.bus_data?.available_seats || 'N/A'}</div>
                  <div>Active: {debugInfo.bus_data?.is_active ? 'Yes' : 'No'}</div>
                </div>
              </div>
              <div>
                <h4 className="font-medium mb-2">Effective Availability</h4>
                <div className="text-sm space-y-1">
                  <div>Effective Available: {debugInfo.effective_availability?.effective_available || 'N/A'}</div>
                  <div>Active Reservations: {debugInfo.effective_availability?.active_reservations || 'N/A'}</div>
                  <div>Is Last Seat: {debugInfo.effective_availability?.is_last_seat ? 'Yes' : 'No'}</div>
                </div>
              </div>
            </div>

            <div className="mb-4">
              <h4 className="font-medium mb-2">Reservation Summary</h4>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-2 text-sm">
                <div>Total: {debugInfo.reservation_summary?.total || 0}</div>
                <div>Active: {debugInfo.reservation_summary?.active || 0}</div>
                <div>Expired: {debugInfo.reservation_summary?.expired_but_not_cleaned || 0}</div>
                <div>Completed: {debugInfo.reservation_summary?.completed || 0}</div>
                <div>Cancelled: {debugInfo.reservation_summary?.cancelled || 0}</div>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">Raw Debug Data</h4>
              <Textarea
                value={JSON.stringify(debugInfo, null, 2)}
                readOnly
                className="h-40 text-xs font-mono"
              />
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>📋 Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent className="text-sm space-y-2">
          <div>1. Ensure your bus route exists and has exactly 1 available seat</div>
          <div>2. Update the bus route code above to match your test route</div>
          <div>3. Click "Test Concurrent Reservations" to simulate race condition</div>
          <div>4. Expected result: Only 1 successful reservation, 2 failures</div>
          <div>5. Use "Get Debug Info" to inspect current state</div>
          <div>6. Use "Force Cleanup" to clean expired reservations</div>
        </CardContent>
      </Card>
    </div>
  );
}
