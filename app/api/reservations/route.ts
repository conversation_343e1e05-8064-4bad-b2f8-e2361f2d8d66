import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { CreateReservationRequest, ReservationResponse } from '@/lib/types';

// POST /api/reservations - Create a new seat reservation
export async function POST(request: NextRequest) {
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2)}`;
  let body: CreateReservationRequest = {} as CreateReservationRequest;

  try {
    body = await request.json();
    const {
      session_id,
      user_identifier,
      bus_route_code,
      departure_time,
      reservation_duration_minutes = 5
    } = body;

    console.log(`🔒 [${requestId}] Reservation request started:`, {
      session_id,
      user_identifier,
      bus_route_code,
      departure_time,
      reservation_duration_minutes,
      timestamp: new Date().toISOString()
    });

    // Validate required fields
    if (!session_id || !user_identifier || !bus_route_code || !departure_time) {
      console.log(`❌ [${requestId}] Missing required fields`);
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: session_id, user_identifier, bus_route_code, departure_time',
        code: 'MISSING_FIELDS'
      } as ReservationResponse, { status: 400 });
    }

    // Validate departure_time format
    const departureDate = new Date(departure_time);
    if (isNaN(departureDate.getTime())) {
      console.log(`❌ [${requestId}] Invalid departure time format:`, departure_time);
      return NextResponse.json({
        success: false,
        error: 'Invalid departure_time format. Use ISO 8601 format.',
        code: 'INVALID_DATE'
      } as ReservationResponse, { status: 400 });
    }

    console.log(`🔒 [${requestId}] Calling database function...`);

    // Call the database function to create reservation
    const { data, error } = await supabaseAdmin.rpc('create_seat_reservation', {
      p_session_id: session_id,
      p_user_identifier: user_identifier,
      p_bus_route_code: bus_route_code,
      p_departure_time: departureDate.toISOString(),
      p_reservation_duration_minutes: reservation_duration_minutes
    });

    // Log the complete error for debugging
    if (error) {
      console.error(`❌ [${requestId}] Database error details:`, {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint,
        fullError: JSON.stringify(error)
      });
    }

    if (error) {
      console.error(`❌ [${requestId}] Database error creating reservation:`, {
        error,
        request_data: { session_id, user_identifier, bus_route_code, departure_time }
      });
      
      // Handle specific error codes from the database function
      if (error.message && error.message.includes('NOT_LAST_SEAT')) {
        // This is an expected condition - the function only allows reservations for the last seat
        console.log(`ℹ️ [${requestId}] Not the last seat scenario - returning available seats information`);
        
        // Get the current seat availability for the response
        const { data: availabilityData } = await supabaseAdmin.rpc('get_effective_seat_availability', {
          p_bus_route_code: bus_route_code,
          p_departure_time: departureDate.toISOString()
        });
        
        return NextResponse.json({
          success: true,
          reservation_needed: false,
          available_seats: availabilityData?.effective_available || 0,
          message: 'Reservation not required - multiple seats available',
          code: 'MULTIPLE_SEATS_AVAILABLE'
        } as ReservationResponse);
      }
      
      if (error.message && error.message.includes('DUPLICATE_RESERVATION')) {
        console.log(`ℹ️ [${requestId}] User already has an active reservation`);
        
        // Get the user's existing reservation
        const { data: checkData } = await supabaseAdmin.rpc('check_seat_reservation', {
          p_user_identifier: user_identifier,
          p_bus_route_code: bus_route_code,
          p_departure_time: departureDate.toISOString()
        });
        
        if (checkData?.has_reservation) {
          return NextResponse.json({
            success: true,
            has_reservation: true,
            reservation_id: checkData.reservation_id,
            expires_at: checkData.expires_at,
            remaining_minutes: checkData.remaining_minutes,
            message: 'User already has an active reservation',
            code: 'EXISTING_RESERVATION'
          } as ReservationResponse);
        }
      }
      
      if (error.message && error.message.includes('ROUTE_NOT_FOUND')) {
        return NextResponse.json({
          success: false,
          error: 'Bus route not found or inactive',
          code: 'ROUTE_NOT_FOUND'
        } as ReservationResponse, { status: 404 });
      }
      
      // Default error handling for other database errors
      return NextResponse.json({
        success: false,
        error: 'Failed to create reservation',
        details: error.message,
        code: 'DATABASE_ERROR'
      } as ReservationResponse, { status: 500 });
    }

    console.log(`✅ [${requestId}] Database function result:`, data);

    // Return the result from the database function
    return NextResponse.json(data as ReservationResponse);

  } catch (error) {
    console.error(`❌ [${requestId}] Error creating seat reservation:`, {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      request_data: {
        session_id: body?.session_id,
        user_identifier: body?.user_identifier,
        bus_route_code: body?.bus_route_code,
        departure_time: body?.departure_time,
        reservation_duration_minutes: body?.reservation_duration_minutes
      },
      timestamp: new Date().toISOString()
    });

    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    } as ReservationResponse, { status: 500 });
  }
}

// GET /api/reservations - Check reservation status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const user_identifier = searchParams.get('user_identifier');
    const bus_route_code = searchParams.get('bus_route_code');
    const departure_time = searchParams.get('departure_time');

    // Validate required parameters
    if (!user_identifier || !bus_route_code || !departure_time) {
      return NextResponse.json({
        success: false,
        has_reservation: false,
        error: 'Missing required parameters: user_identifier, bus_route_code, departure_time'
      }, { status: 400 });
    }

    // Validate departure_time format
    const departureDate = new Date(departure_time);
    if (isNaN(departureDate.getTime())) {
      return NextResponse.json({
        success: false,
        has_reservation: false,
        error: 'Invalid departure_time format. Use ISO 8601 format.'
      }, { status: 400 });
    }

    // Call the database function to check reservation
    const { data, error } = await supabaseAdmin.rpc('check_seat_reservation', {
      p_user_identifier: user_identifier,
      p_bus_route_code: bus_route_code,
      p_departure_time: departureDate.toISOString()
    });

    if (error) {
      console.error('Database error checking reservation:', error);
      return NextResponse.json({
        success: false,
        has_reservation: false,
        error: 'Failed to check reservation status'
      }, { status: 500 });
    }

    return NextResponse.json(data);

  } catch (error) {
    const { searchParams } = new URL(request.url);
    const user_identifier = searchParams.get('user_identifier');
    const bus_route_code = searchParams.get('bus_route_code');
    const departure_time = searchParams.get('departure_time');

    console.error('Error checking seat reservation:', {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      request_params: {
        user_identifier,
        bus_route_code,
        departure_time
      },
      timestamp: new Date().toISOString()
    });

    return NextResponse.json({
      success: false,
      has_reservation: false,
      error: 'Internal server error',
      error_code: 'RESERVATION_CHECK_ERROR'
    }, { status: 500 });
  }
}

// DELETE /api/reservations - Release/cancel a reservation
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const reservation_id = searchParams.get('reservation_id');
    const user_identifier = searchParams.get('user_identifier');
    const reason = searchParams.get('reason') || 'manual_release';

    // Validate required parameters
    if (!reservation_id || !user_identifier) {
      return NextResponse.json({
        success: false,
        error: 'Missing required parameters: reservation_id, user_identifier'
      }, { status: 400 });
    }

    // Validate reservation_id is a number
    const reservationIdNum = parseInt(reservation_id, 10);
    if (isNaN(reservationIdNum)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid reservation_id. Must be a number.'
      }, { status: 400 });
    }

    // Call the database function to release reservation
    const { data, error } = await supabaseAdmin.rpc('release_seat_reservation', {
      p_reservation_id: reservationIdNum,
      p_user_identifier: user_identifier,
      p_reason: reason
    });

    if (error) {
      console.error('Database error releasing reservation:', error);
      return NextResponse.json({
        success: false,
        error: 'Failed to release reservation'
      }, { status: 500 });
    }

    return NextResponse.json(data);

  } catch (error) {
    console.error('Error releasing seat reservation:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
