import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';

// GET /api/reservations/statistics - Get reservation statistics for monitoring
export async function GET(request: NextRequest) {
  try {
    // Call the database function to get reservation statistics
    const { data, error } = await supabaseAdmin.rpc('get_reservation_statistics');

    if (error) {
      console.error('Database error getting reservation statistics:', error);
      return NextResponse.json({
        success: false,
        error: 'Failed to get reservation statistics'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      statistics: data,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error getting reservation statistics:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
