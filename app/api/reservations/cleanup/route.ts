import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';

// POST /api/reservations/cleanup - Clean up expired reservations
export async function POST(request: NextRequest) {
  try {
    // Call the database function to cleanup expired reservations
    const { data, error } = await supabaseAdmin.rpc('cleanup_expired_reservations');

    if (error) {
      console.error('Database error cleaning up reservations:', error);
      return NextResponse.json({
        success: false,
        error: 'Failed to cleanup expired reservations'
      }, { status: 500 });
    }

    return NextResponse.json(data);

  } catch (error) {
    console.error('Error cleaning up expired reservations:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// GET /api/reservations/cleanup - Get cleanup status (for monitoring)
export async function GET(request: NextRequest) {
  try {
    // Get count of expired but not cleaned up reservations
    const { data: expiredCount, error: expiredError } = await supabaseAdmin
      .from('seat_reservations')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active')
      .lt('expires_at', new Date().toISOString());

    if (expiredError) {
      console.error('Error getting expired reservations count:', expiredError);
      return NextResponse.json({
        success: false,
        error: 'Failed to get cleanup status'
      }, { status: 500 });
    }

    // Get count of active reservations
    const { data: activeCount, error: activeError } = await supabaseAdmin
      .from('seat_reservations')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active')
      .gt('expires_at', new Date().toISOString());

    if (activeError) {
      console.error('Error getting active reservations count:', activeError);
      return NextResponse.json({
        success: false,
        error: 'Failed to get cleanup status'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      active_reservations: activeCount || 0,
      expired_reservations_needing_cleanup: expiredCount || 0,
      cleanup_needed: (expiredCount || 0) > 0
    });

  } catch (error) {
    console.error('Error getting cleanup status:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
