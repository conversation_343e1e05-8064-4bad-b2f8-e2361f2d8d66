import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { EffectiveSeatAvailabilityResponse } from '@/lib/types';

// GET /api/reservations/availability - Get effective seat availability considering active reservations
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const bus_route_code = searchParams.get('bus_route_code');
    const departure_time = searchParams.get('departure_time');

    // Validate required parameters
    if (!bus_route_code) {
      return NextResponse.json({
        success: false,
        error: 'Missing required parameter: bus_route_code'
      } as EffectiveSeatAvailabilityResponse, { status: 400 });
    }

    // Use current time if departure_time not provided
    let departureDate = new Date();
    if (departure_time) {
      departureDate = new Date(departure_time);
      if (isNaN(departureDate.getTime())) {
        return NextResponse.json({
          success: false,
          error: 'Invalid departure_time format. Use ISO 8601 format.'
        } as EffectiveSeatAvailabilityResponse, { status: 400 });
      }
    }

    // Call the database function to get effective availability
    const { data, error } = await supabaseAdmin.rpc('get_effective_seat_availability', {
      p_bus_route_code: bus_route_code,
      p_departure_time: departureDate.toISOString()
    });

    if (error) {
      console.error('Database error getting effective availability:', error);
      return NextResponse.json({
        success: false,
        error: 'Failed to get seat availability'
      } as EffectiveSeatAvailabilityResponse, { status: 500 });
    }

    return NextResponse.json(data as EffectiveSeatAvailabilityResponse);

  } catch (error) {
    console.error('Error getting effective seat availability:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as EffectiveSeatAvailabilityResponse, { status: 500 });
  }
}
