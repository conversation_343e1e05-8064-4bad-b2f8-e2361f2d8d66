import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';

// POST /api/reservations/complete - Complete a reservation when booking is confirmed
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      user_identifier, 
      bus_route_code, 
      departure_time,
      booking_id 
    } = body;

    // Validate required fields
    if (!user_identifier || !bus_route_code || !departure_time || !booking_id) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: user_identifier, bus_route_code, departure_time, booking_id'
      }, { status: 400 });
    }

    // Validate departure_time format
    const departureDate = new Date(departure_time);
    if (isNaN(departureDate.getTime())) {
      return NextResponse.json({
        success: false,
        error: 'Invalid departure_time format. Use ISO 8601 format.'
      }, { status: 400 });
    }

    // Call the database function to complete reservation
    const { data, error } = await supabaseAdmin.rpc('complete_seat_reservation', {
      p_user_identifier: user_identifier,
      p_bus_route_code: bus_route_code,
      p_departure_time: departureDate.toISOString(),
      p_booking_id: booking_id
    });

    if (error) {
      console.error('Database error completing reservation:', error);
      return NextResponse.json({
        success: false,
        error: 'Failed to complete reservation'
      }, { status: 500 });
    }

    return NextResponse.json(data);

  } catch (error) {
    console.error('Error completing seat reservation:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
