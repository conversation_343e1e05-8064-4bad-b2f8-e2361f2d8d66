import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseAdmin } from '@/lib/supabase-admin';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const busRoute = searchParams.get('busRoute');
    
    if (!busRoute) {
      return NextResponse.json({
        success: false,
        error: 'Missing required parameter: busRoute'
      }, { status: 400 });
    }
    
    // Call the test function to check if reservation system is working
    const { data, error } = await getSupabaseAdmin().rpc('test_reservation_system', {
      p_bus_route_code: busRoute
    });
    
    if (error) {
      console.error('Error testing reservation system:', error);
      return NextResponse.json({
        success: false,
        error: 'Failed to test reservation system',
        details: error.message,
        code: 'TEST_ERROR'
      }, { status: 500 });
    }
    
    return NextResponse.json({
      success: true,
      test_result: data,
      message: 'Reservation system test completed'
    });
    
  } catch (error) {
    console.error('Unexpected error in test-reservation API:', error);
    return NextResponse.json({
      success: false,
      error: 'Server error',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}