import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseAdmin } from '@/lib/supabase-admin';

// GET /api/debug/reservations - Debug endpoint to check reservation state
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const bus_route_code = searchParams.get('bus_route_code');
    const departure_time = searchParams.get('departure_time');

    // Get all reservations for debugging
    let query = getSupabaseAdmin()
      .from('seat_reservations')
      .select('*')
      .order('created_at', { ascending: false });

    if (bus_route_code) {
      query = query.eq('bus_route_code', bus_route_code);
    }

    if (departure_time) {
      query = query.eq('departure_time', departure_time);
    }

    const { data: reservations, error: reservationsError } = await query;

    if (reservationsError) {
      console.error('Error fetching reservations:', reservationsError);
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch reservations'
      }, { status: 500 });
    }

    // Get bus information
    let busData = null;
    if (bus_route_code) {
      const { data: bus, error: busError } = await getSupabaseAdmin()
        .from('buses')
        .select('*')
        .eq('route_code', bus_route_code)
        .eq('is_active', true)
        .single();

      if (!busError) {
        busData = bus;
      }
    }

    // Get effective availability if route specified
    let effectiveAvailability = null;
    if (bus_route_code && departure_time) {
      const { data: availability, error: availabilityError } = await getSupabaseAdmin().rpc('get_effective_seat_availability', {
        p_bus_route_code: bus_route_code,
        p_departure_time: departure_time
      });

      if (!availabilityError) {
        effectiveAvailability = availability;
      }
    }

    // Analyze reservations
    const now = new Date();
    interface Reservation {
      status: 'active' | 'completed' | 'cancelled';
      expires_at: string;
    }

    const activeReservations: Reservation[] = reservations?.filter((r: Reservation) => 
      r.status === 'active' && new Date(r.expires_at) > now
    ) || [];
    
    const expiredReservations = reservations?.filter(r => 
      r.status === 'active' && new Date(r.expires_at) <= now
    ) || [];

    const completedReservations = reservations?.filter(r => 
      r.status === 'completed'
    ) || [];

    const cancelledReservations = reservations?.filter(r => 
      r.status === 'cancelled'
    ) || [];

    return NextResponse.json({
      success: true,
      debug_info: {
        timestamp: now.toISOString(),
        bus_route_code,
        departure_time,
        bus_data: busData,
        effective_availability: effectiveAvailability,
        reservation_summary: {
          total: reservations?.length || 0,
          active: activeReservations.length,
          expired_but_not_cleaned: expiredReservations.length,
          completed: completedReservations.length,
          cancelled: cancelledReservations.length
        },
        reservations: {
          active: activeReservations,
          expired_not_cleaned: expiredReservations,
          completed: completedReservations,
          cancelled: cancelledReservations
        }
      }
    });

  } catch (error) {
    console.error('Debug endpoint error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// POST /api/debug/reservations - Force cleanup expired reservations
export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'cleanup') {
      // Force cleanup of expired reservations
      const { data, error } = await getSupabaseAdmin().rpc('cleanup_expired_reservations');

      if (error) {
        console.error('Error during forced cleanup:', error);
        return NextResponse.json({
          success: false,
          error: 'Failed to cleanup expired reservations'
        }, { status: 500 });
      }

      return NextResponse.json({
        success: true,
        message: 'Cleanup completed',
        result: data
      });
    }

    return NextResponse.json({
      success: false,
      error: 'Invalid action. Use ?action=cleanup'
    }, { status: 400 });

  } catch (error) {
    console.error('Debug cleanup error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
