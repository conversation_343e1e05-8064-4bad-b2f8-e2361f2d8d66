import { NextResponse } from 'next/server';
import { supabase, supabaseAdmin } from '@/lib/supabase';

export async function GET() {
  try {
    const { data, error } = await supabase
      .from('buses')
      .select('route_code, available_seats')
      .eq('is_active', true);

    if (error) {
      throw error;
    }

    const availability: { [key: string]: any } = {};

    // Get effective availability for each bus route
    for (const bus of data || []) {
      try {
        // Get effective seat availability considering reservations
        const { data: effectiveData, error: effectiveError } = await supabaseAdmin.rpc('get_effective_seat_availability', {
          p_bus_route_code: bus.route_code,
          p_departure_time: new Date().toISOString()
        });

        if (effectiveError) {
          console.error(`Error getting effective availability for ${bus.route_code}:`, effectiveError);
          // Fallback to basic availability
          availability[bus.route_code] = {
            total_seats: bus.available_seats,
            effective_available: bus.available_seats,
            active_reservations: 0,
            is_last_seat: bus.available_seats === 1
          };
        } else {
          availability[bus.route_code] = effectiveData;
        }
      } catch (error) {
        console.error(`Error processing availability for ${bus.route_code}:`, error);
        // Fallback to basic availability
        availability[bus.route_code] = {
          total_seats: bus.available_seats,
          effective_available: bus.available_seats,
          active_reservations: 0,
          is_last_seat: bus.available_seats === 1
        };
      }
    }

    return NextResponse.json(availability);
  } catch (error) {
    console.error('Error getting bus availability:', error);
    return NextResponse.json({}, { status: 500 });
  }
}